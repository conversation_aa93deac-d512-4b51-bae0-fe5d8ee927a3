import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Clock,
  Zap,
  Globe,
  Server,
  Activity
} from 'lucide-react';

interface NetworkStatus {
  api_reachable: boolean;
  response_time: number;
  error_message?: string;
}

interface TestResult {
  name: string;
  url: string;
  status: 'pending' | 'success' | 'failed' | 'testing';
  responseTime?: number;
  error?: string;
}

export const NetworkTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [overallStatus, setOverallStatus] = useState<'good' | 'warning' | 'error' | 'unknown'>('unknown');

  const testEndpoints = [
    { name: '解析API', url: 'https://dp.wpurl.cc/api/v1/user/token' },
    { name: '百度网盘', url: 'https://pan.baidu.com' },
    { name: 'DNS解析', url: 'https://8.8.8.8' },
    { name: '网络连通性', url: 'https://www.baidu.com' },
  ];

  const runFullNetworkTest = async () => {
    setTesting(true);
    setOverallStatus('unknown');
    
    // 初始化测试结果
    const initialResults: TestResult[] = testEndpoints.map(endpoint => ({
      ...endpoint,
      status: 'pending'
    }));
    setTestResults(initialResults);

    let successCount = 0;
    const updatedResults: TestResult[] = [];

    for (let i = 0; i < testEndpoints.length; i++) {
      const endpoint = testEndpoints[i];
      
      // 更新当前测试状态
      setTestResults(prev => prev.map((result, index) => 
        index === i ? { ...result, status: 'testing' } : result
      ));

      try {
        let testResult: TestResult;
        
        if (endpoint.name === '解析API') {
          // 使用我们的API检测函数
          const status: NetworkStatus = await invoke('check_network');
          testResult = {
            ...endpoint,
            status: status.api_reachable ? 'success' : 'failed',
            responseTime: status.response_time,
            error: status.error_message
          };
        } else {
          // 模拟其他网络测试（实际项目中可以添加更多检测逻辑）
          const startTime = Date.now();
          try {
            // 这里可以添加实际的网络检测逻辑
            await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
            const responseTime = Date.now() - startTime;
            testResult = {
              ...endpoint,
              status: 'success',
              responseTime
            };
          } catch (error) {
            testResult = {
              ...endpoint,
              status: 'failed',
              error: error instanceof Error ? error.message : '连接失败'
            };
          }
        }

        if (testResult.status === 'success') {
          successCount++;
        }

        updatedResults.push(testResult);
        
        // 更新结果
        setTestResults(prev => prev.map((result, index) => 
          index === i ? testResult : result
        ));

      } catch (error) {
        const failedResult: TestResult = {
          ...endpoint,
          status: 'failed',
          error: error instanceof Error ? error.message : '测试失败'
        };
        updatedResults.push(failedResult);
        
        setTestResults(prev => prev.map((result, index) => 
          index === i ? failedResult : result
        ));
      }
    }

    // 确定整体状态
    if (successCount === testEndpoints.length) {
      setOverallStatus('good');
    } else if (successCount >= testEndpoints.length / 2) {
      setOverallStatus('warning');
    } else {
      setOverallStatus('error');
    }

    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'testing':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'pending':
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getOverallStatusInfo = () => {
    switch (overallStatus) {
      case 'good':
        return {
          icon: <CheckCircle className="w-6 h-6 text-green-500" />,
          text: '网络状态良好',
          color: 'text-green-600',
          bgColor: 'bg-green-50'
        };
      case 'warning':
        return {
          icon: <AlertTriangle className="w-6 h-6 text-yellow-500" />,
          text: '网络状态一般',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50'
        };
      case 'error':
        return {
          icon: <XCircle className="w-6 h-6 text-red-500" />,
          text: '网络状态异常',
          color: 'text-red-600',
          bgColor: 'bg-red-50'
        };
      default:
        return {
          icon: <Activity className="w-6 h-6 text-gray-500" />,
          text: '等待测试',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50'
        };
    }
  };

  const statusInfo = getOverallStatusInfo();

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <Globe className="w-6 h-6 mr-2" />
          全能网络测试
        </h2>
        <button
          onClick={runFullNetworkTest}
          disabled={testing}
          className="btn-primary flex items-center space-x-2"
        >
          {testing ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>测试中...</span>
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              <span>开始测试</span>
            </>
          )}
        </button>
      </div>

      {/* 整体状态 */}
      {testResults.length > 0 && (
        <div className={`${statusInfo.bgColor} rounded-lg p-4 mb-6`}>
          <div className="flex items-center space-x-3">
            {statusInfo.icon}
            <div>
              <h3 className={`font-medium ${statusInfo.color}`}>
                {statusInfo.text}
              </h3>
              <p className="text-sm text-gray-600">
                {testResults.filter(r => r.status === 'success').length} / {testResults.length} 项测试通过
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 测试结果列表 */}
      {testResults.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-gray-900">测试详情</h3>
          {testResults.map((result, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(result.status)}
                  <div>
                    <h4 className="font-medium text-gray-900">{result.name}</h4>
                    <p className="text-sm text-gray-500">{result.url}</p>
                  </div>
                </div>
                <div className="text-right">
                  {result.responseTime && (
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Zap className="w-4 h-4" />
                      <span>{result.responseTime}ms</span>
                    </div>
                  )}
                  {result.error && (
                    <p className="text-sm text-red-600 mt-1">{result.error}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 测试说明 */}
      <div className="mt-6 bg-blue-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">测试说明</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• 解析API: 测试与解析服务器的连接状态</li>
          <li>• 百度网盘: 测试百度网盘服务的可访问性</li>
          <li>• DNS解析: 测试DNS解析服务是否正常</li>
          <li>• 网络连通性: 测试基本的网络连接</li>
        </ul>
      </div>
    </div>
  );
};
