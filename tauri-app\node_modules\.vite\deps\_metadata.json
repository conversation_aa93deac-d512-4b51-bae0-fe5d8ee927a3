{"hash": "7a3a25ae", "browserHash": "87b45239", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "703fc2eb", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "cb79d97f", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "889aca90", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "921c2a46", "needsInterop": true}, "@tauri-apps/api/tauri": {"src": "../../@tauri-apps/api/tauri.js", "file": "@tauri-apps_api_tauri.js", "fileHash": "4a40280d", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "1f6a42ca", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "80a769da", "needsInterop": true}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}