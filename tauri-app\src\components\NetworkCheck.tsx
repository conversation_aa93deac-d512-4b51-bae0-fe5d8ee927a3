import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertT<PERSON>gle,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON>,
  Zap
} from 'lucide-react';

interface NetworkStatus {
  api_reachable: boolean;
  response_time: number;
  error_message?: string;
}

interface NetworkCheckProps {
  onStatusChange?: (status: 'connected' | 'disconnected' | 'checking') => void;
}

export const NetworkCheck: React.FC<NetworkCheckProps> = ({ onStatusChange }) => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus | null>(null);
  const [checking, setChecking] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState<Date | null>(null);

  const checkNetwork = async () => {
    setChecking(true);
    onStatusChange?.('checking');

    try {
      const status: NetworkStatus = await invoke('check_network');
      setNetworkStatus(status);
      setLastCheckTime(new Date());
      onStatusChange?.(status.api_reachable ? 'connected' : 'disconnected');
    } catch (error) {
      console.error('网络检测失败:', error);
      setNetworkStatus({
        api_reachable: false,
        response_time: 0,
        error_message: '检测失败'
      });
      onStatusChange?.('disconnected');
    } finally {
      setChecking(false);
    }
  };

  const getStatusIcon = () => {
    if (checking) {
      return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
    }
    
    if (!networkStatus) {
      return <Wifi className="w-5 h-5 text-gray-400" />;
    }

    return networkStatus.api_reachable 
      ? <CheckCircle className="w-5 h-5 text-green-500" />
      : <XCircle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = () => {
    if (checking) return '检测中...';
    if (!networkStatus) return '未检测';
    return networkStatus.api_reachable ? '连接正常' : '连接失败';
  };

  const getStatusColor = () => {
    if (checking) return 'text-blue-600';
    if (!networkStatus) return 'text-gray-600';
    return networkStatus.api_reachable ? 'text-green-600' : 'text-red-600';
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 500) return 'text-green-600';
    if (time < 1000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatLastCheck = () => {
    if (!lastCheckTime) return '';
    const now = new Date();
    const diff = Math.floor((now.getTime() - lastCheckTime.getTime()) / 1000);
    
    if (diff < 60) return `${diff}秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
    return lastCheckTime.toLocaleTimeString();
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center">
          <Wifi className="w-5 h-5 mr-2" />
          网络检测
        </h3>
      </div>
      <div className="card-content space-y-4">
        {/* 检测按钮 */}
        <button
          onClick={checkNetwork}
          disabled={checking}
          className="w-full btn-primary flex items-center justify-center space-x-2"
        >
          {checking ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>检测中...</span>
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              <span>检测网络</span>
            </>
          )}
        </button>

        {/* 状态显示 */}
        <div className="space-y-3">
          {/* 连接状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">API 连接</span>
            </div>
            <span className={`text-sm font-bold ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>

          {/* 响应时间 */}
          {networkStatus && networkStatus.api_reachable && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">响应时间</span>
              </div>
              <span className={`text-sm font-bold ${getResponseTimeColor(networkStatus.response_time)}`}>
                {networkStatus.response_time}ms
              </span>
            </div>
          )}

          {/* 最后检测时间 */}
          {lastCheckTime && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium">最后检测</span>
              </div>
              <span className="text-sm text-gray-600">
                {formatLastCheck()}
              </span>
            </div>
          )}

          {/* 错误信息 */}
          {networkStatus && !networkStatus.api_reachable && networkStatus.error_message && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-red-800">连接失败</p>
                  <p className="text-xs text-red-600 mt-1">
                    {networkStatus.error_message}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 网络状态指示器 */}
          {networkStatus && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">网络状态</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    networkStatus.api_reachable ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className={networkStatus.api_reachable ? 'text-green-600' : 'text-red-600'}>
                    {networkStatus.api_reachable ? '在线' : '离线'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 帮助信息 */}
        <div className="bg-blue-50 p-3 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-1">网络检测说明</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• 检测与解析服务器的连接状态</li>
            <li>• 测量网络响应延迟</li>
            <li>• 建议响应时间 &lt; 1000ms</li>
            <li>• 如连接失败请检查网络设置</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
