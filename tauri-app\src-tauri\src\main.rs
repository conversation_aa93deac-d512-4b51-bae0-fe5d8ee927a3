// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{State, Manager, SystemTray, SystemTrayMenu, SystemTrayMenuItem, CustomMenuItem, SystemTrayEvent, AppHandle};
use tokio::sync::Mutex;
use sysinfo::{System, SystemExt, CpuExt};

#[derive(Debug, Serialize, Deserialize)]
struct FileInfo {
    fs_id: u64,
    is_dir: bool,
    server_filename: String,
    size: u64,
    path: String,
    server_mtime: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct ApiResponse {
    code: u32,
    message: String,
    data: Option<ApiData>,
}

#[derive(Debug, Serialize, Deserialize)]
struct ApiData {
    uk: Option<u64>,
    shareid: Option<u64>,
    randsk: Option<String>,
    list: Vec<FileInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
struct DownloadLink {
    filename: String,
    urls: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct DownloadResponse {
    code: u32,
    message: String,
    data: Vec<DownloadLink>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SystemInfo {
    cpu_usage: f32,
    memory_usage: f32,
    total_memory: u64,
    used_memory: u64,
    process_count: usize,
    uptime: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct LicenseInfo {
    token: String,
    expires_at: Option<String>,
    user_id: Option<String>,
    plan: String,
    status: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct TokenValidationResponse {
    code: u32,
    message: String,
    data: Option<TokenData>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TokenData {
    count: u64,
    expires_at: Option<String>,
    ip: Vec<String>,
    length: u64,
    remaining_count: u64,
    remaining_size: u64,
    size: u64,
    token: String,
    used_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct NetworkStatus {
    api_reachable: bool,
    response_time: u64,
    error_message: Option<String>,
}

// 应用状态
#[derive(Default)]
struct AppState {
    uk: Option<u64>,
    shareid: Option<u64>,
    randsk: Option<String>,
}

type AppStateType = Mutex<AppState>;

// 生成随机参数
fn generate_rand_params() -> (String, String, String) {
    use uuid::Uuid;
    let rand1 = Uuid::new_v4().to_string().replace("-", "");
    let rand2 = Uuid::new_v4().to_string().replace("-", "")[..32].to_string();
    let rand3 = Uuid::new_v4().to_string().replace("-", "");
    (rand1, rand2, rand3)
}

// 获取文件列表
#[tauri::command]
async fn get_file_list(
    token: String,
    url: String,
    pwd: String,
    dir: String,
    state: State<'_, AppStateType>,
) -> Result<ApiResponse, String> {
    let (rand1, rand2, rand3) = generate_rand_params();
    
    let surl = url.split('/').last().unwrap_or("").replace("?", "");
    
    let mut payload = HashMap::new();
    payload.insert("token", token);
    payload.insert("url", url);
    payload.insert("pwd", pwd);
    payload.insert("dir", dir);
    payload.insert("surl", surl);
    payload.insert("parse_password", "".to_string());
    payload.insert("rand1", rand1);
    payload.insert("rand2", rand2);
    payload.insert("rand3", rand3);

    let client = reqwest::Client::new();
    let response = client
        .post("https://dp.wpurl.cc/api/v1/user/parse/get_file_list")
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    let api_response: ApiResponse = response.json().await.map_err(|e| e.to_string())?;

    // 保存状态信息
    if let Some(data) = &api_response.data {
        let mut app_state = state.lock().await;
        app_state.uk = data.uk;
        app_state.shareid = data.shareid;
        app_state.randsk = data.randsk.clone();
    }

    Ok(api_response)
}

// 获取下载链接
#[tauri::command]
async fn get_download_links(
    token: String,
    url: String,
    pwd: String,
    dir: String,
    fs_ids: Vec<u64>,
    state: State<'_, AppStateType>,
) -> Result<DownloadResponse, String> {
    let (rand1, rand2, rand3) = generate_rand_params();
    
    let surl = url.split('/').last().unwrap_or("").replace("?", "");
    
    let app_state = state.lock().await;
    
    let mut payload = HashMap::new();
    payload.insert("token", token);
    payload.insert("url", url);
    payload.insert("pwd", pwd);
    payload.insert("dir", dir);
    payload.insert("fs_id", serde_json::to_string(&fs_ids).unwrap());
    payload.insert("randsk", app_state.randsk.clone().unwrap_or_default());
    payload.insert("uk", app_state.uk.unwrap_or(0).to_string());
    payload.insert("shareid", app_state.shareid.unwrap_or(0).to_string());
    payload.insert("surl", surl);
    payload.insert("parse_password", "".to_string());
    payload.insert("vcode_str", "".to_string());
    payload.insert("vcode_input", "".to_string());
    payload.insert("rand1", rand1);
    payload.insert("rand2", rand2);
    payload.insert("rand3", rand3);

    drop(app_state); // 释放锁

    let client = reqwest::Client::new();
    let response = client
        .post("https://dp.wpurl.cc/api/v1/user/parse/get_download_links")
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    let download_response: DownloadResponse = response.json().await.map_err(|e| e.to_string())?;

    Ok(download_response)
}

// 调用IDM下载
#[tauri::command]
async fn call_idm_download(download_url: String, filename: String, download_path: String) -> Result<bool, String> {
    use std::process::Command;
    
    // 查找IDM路径
    let idm_paths = vec![
        r"C:\Program Files (x86)\Internet Download Manager\IDMan.exe",
        r"C:\Program Files\Internet Download Manager\IDMan.exe",
        r"D:\Program Files (x86)\Internet Download Manager\IDMan.exe",
        r"D:\Program Files\Internet Download Manager\IDMan.exe",
    ];
    
    let mut idm_path = None;
    for path in idm_paths {
        if std::path::Path::new(path).exists() {
            idm_path = Some(path);
            break;
        }
    }
    
    let idm_exe = idm_path.ok_or("IDM not found")?;
    
    // 创建下载目录
    std::fs::create_dir_all(&download_path).map_err(|e| e.to_string())?;
    
    // 调用IDM
    let output = Command::new(idm_exe)
        .args(&["/d", &download_url, "/p", &download_path, "/f", &filename, "/a"])
        .output()
        .map_err(|e| e.to_string())?;
    
    Ok(output.status.success())
}

// 获取系统信息
#[tauri::command]
async fn get_system_info() -> Result<SystemInfo, String> {
    let mut sys = System::new_all();
    sys.refresh_all();

    let cpu_usage = sys.global_cpu_info().cpu_usage();
    let total_memory = sys.total_memory();
    let used_memory = sys.used_memory();
    let memory_usage = (used_memory as f32 / total_memory as f32) * 100.0;
    let process_count = sys.processes().len();
    let uptime = sys.uptime();

    Ok(SystemInfo {
        cpu_usage,
        memory_usage,
        total_memory,
        used_memory,
        process_count,
        uptime,
    })
}

// 验证卡密
#[tauri::command]
async fn validate_token(token: String) -> Result<TokenValidationResponse, String> {
    let mut payload = HashMap::new();
    payload.insert("token", token.clone());

    let client = reqwest::Client::new();
    let response = client
        .post("https://dp.wpurl.cc/api/v1/user/token/info")
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| e.to_string())?;

    let validation_response: TokenValidationResponse = response.json().await.map_err(|e| e.to_string())?;
    Ok(validation_response)
}

// 获取授权信息
#[tauri::command]
async fn get_license_info(token: String) -> Result<LicenseInfo, String> {
    // 调用验证API获取真实信息
    match validate_token(token.clone()).await {
        Ok(response) => {
            if response.code == 200 && response.data.is_some() {
                let data = response.data.unwrap();
                Ok(LicenseInfo {
                    token: data.token,
                    expires_at: data.expires_at,
                    user_id: None,
                    plan: if data.remaining_count > 500000 { "高级版".to_string() } else { "标准版".to_string() },
                    status: "有效".to_string(),
                })
            } else {
                Ok(LicenseInfo {
                    token: token.clone(),
                    expires_at: None,
                    user_id: None,
                    plan: "未知".to_string(),
                    status: "无效".to_string(),
                })
            }
        }
        Err(_) => {
            Ok(LicenseInfo {
                token: token.clone(),
                expires_at: None,
                user_id: None,
                plan: "未知".to_string(),
                status: "无效".to_string(),
            })
        }
    }
}

// 保存配置
#[tauri::command]
async fn save_config(config: HashMap<String, String>) -> Result<(), String> {
    let config_path = std::env::current_dir()
        .map_err(|e| e.to_string())?
        .join("config.json");

    let config_json = serde_json::to_string_pretty(&config)
        .map_err(|e| e.to_string())?;

    std::fs::write(config_path, config_json)
        .map_err(|e| e.to_string())?;

    Ok(())
}

// 加载配置
#[tauri::command]
async fn load_config() -> Result<HashMap<String, String>, String> {
    let config_path = std::env::current_dir()
        .map_err(|e| e.to_string())?
        .join("config.json");

    if !config_path.exists() {
        return Ok(HashMap::new());
    }

    let config_content = std::fs::read_to_string(config_path)
        .map_err(|e| e.to_string())?;

    let config: HashMap<String, String> = serde_json::from_str(&config_content)
        .map_err(|e| e.to_string())?;

    Ok(config)
}

// 检测网络连接
#[tauri::command]
async fn check_network() -> Result<NetworkStatus, String> {
    let start_time = std::time::Instant::now();

    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    match client
        .get("https://dp.wpurl.cc/api/v1/user/token/info")
        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .send()
        .await
    {
        Ok(response) => {
            let response_time = start_time.elapsed().as_millis() as u64;
            let status = response.status();

            if status.is_success() || status.as_u16() == 400 {
                // 400 也算成功，因为API可达，只是参数问题
                Ok(NetworkStatus {
                    api_reachable: true,
                    response_time,
                    error_message: None,
                })
            } else {
                Ok(NetworkStatus {
                    api_reachable: false,
                    response_time,
                    error_message: Some(format!("HTTP {}", status.as_u16())),
                })
            }
        }
        Err(e) => {
            let response_time = start_time.elapsed().as_millis() as u64;
            Ok(NetworkStatus {
                api_reachable: false,
                response_time,
                error_message: Some(e.to_string()),
            })
        }
    }
}

// 系统托盘事件处理
fn handle_system_tray_event(app: &AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick { .. } => {
            let window = app.get_window("main").unwrap();
            window.show().unwrap();
            window.set_focus().unwrap();
        }
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                    window.set_focus().unwrap();
                }
                "hide" => {
                    let window = app.get_window("main").unwrap();
                    window.hide().unwrap();
                }
                "quit" => {
                    std::process::exit(0);
                }
                _ => {}
            }
        }
        _ => {}
    }
}

fn main() {
    // 创建系统托盘菜单
    let show = CustomMenuItem::new("show".to_string(), "显示窗口");
    let hide = CustomMenuItem::new("hide".to_string(), "隐藏窗口");
    let quit = CustomMenuItem::new("quit".to_string(), "退出程序");

    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    let system_tray = SystemTray::new()
        .with_menu(tray_menu)
        .with_tooltip("百度网盘解析工具");

    tauri::Builder::default()
        .manage(AppStateType::default())
        .system_tray(system_tray)
        .on_system_tray_event(handle_system_tray_event)
        .invoke_handler(tauri::generate_handler![
            get_file_list,
            get_download_links,
            call_idm_download,
            get_system_info,
            validate_token,
            get_license_info,
            save_config,
            load_config,
            check_network
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
