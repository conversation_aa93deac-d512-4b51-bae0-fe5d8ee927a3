{"rustc": 16591470773350601817, "features": "[\"arboard\", \"clipboard\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 6634292764194895369, "deps": [[4381063397040571828, "webview2_com", false, 11290250088670576088], [5788395569483560775, "arboard", false, 14077625922794439019], [7653476968652377684, "windows", false, 10690132649445514100], [8292277814562636972, "tauri_utils", false, 17068750469945825521], [8319709847752024821, "uuid", false, 17329545268604777101], [8391357152270261188, "wry", false, 4220231073951058425], [11693073011723388840, "raw_window_handle", false, 14717548697597070664], [13208667028893622512, "rand", false, 13122419021854910965], [14162324460024849578, "tauri_runtime", false, 12444769989192652711], [16228250612241359704, "build_script_build", false, 8980042063070834766]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-8c03a0cd46119da0\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}