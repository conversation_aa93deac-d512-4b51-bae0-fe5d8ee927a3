{"rustc": 16591470773350601817, "features": "[\"clipboard\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 7590075709875789473, "deps": [[3150220818285335163, "url", false, 9721221189520161498], [4381063397040571828, "webview2_com", false, 11290250088670576088], [4405182208873388884, "http", false, 5574349850812844607], [7653476968652377684, "windows", false, 10690132649445514100], [8008191657135824715, "thiserror", false, 10567539360728756001], [8292277814562636972, "tauri_utils", false, 17068750469945825521], [8319709847752024821, "uuid", false, 17329545268604777101], [8866577183823226611, "http_range", false, 11783221401813674897], [9689903380558560274, "serde", false, 3990983680525661946], [11693073011723388840, "raw_window_handle", false, 14717548697597070664], [13208667028893622512, "rand", false, 13122419021854910965], [14162324460024849578, "build_script_build", false, 12766383824652850471], [15367738274754116744, "serde_json", false, 16765817478399170525]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-72acc76a169382f4\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}