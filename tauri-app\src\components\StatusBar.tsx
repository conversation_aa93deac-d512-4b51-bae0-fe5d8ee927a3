import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Wifi, 
  WifiOff, 
  Clock, 
  HardDrive, 
  Cpu, 
  MemoryStick,
  Activity,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface StatusBarProps {
  connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'checking';
  selectedCount: number;
  totalFiles: number;
  currentOperation?: string;
}

interface SystemInfo {
  cpu_usage: number;
  memory_usage: number;
  total_memory: number;
  used_memory: number;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  connectionStatus,
  selectedCount,
  totalFiles,
  currentOperation
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 获取系统信息
  useEffect(() => {
    const fetchSystemInfo = async () => {
      try {
        const info: SystemInfo = await invoke('get_system_info');
        setSystemInfo(info);
      } catch (error) {
        console.error('获取系统信息失败:', error);
      }
    };

    fetchSystemInfo();
    const interval = setInterval(fetchSystemInfo, 5000); // 每5秒更新一次
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Activity className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'disconnected':
      default:
        return <WifiOff className="w-4 h-4 text-red-500" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected':
        return '已连接';
      case 'connecting':
        return '连接中...';
      case 'checking':
        return '检测中...';
      case 'disconnected':
      default:
        return '未连接';
    }
  };

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600';
      case 'connecting':
      case 'checking':
        return 'text-yellow-600';
      case 'disconnected':
      default:
        return 'text-red-600';
    }
  };

  return (
    <div className="bg-gray-100 border-t border-gray-200 px-4 py-2">
      <div className="flex items-center justify-between text-sm">
        {/* 左侧信息 */}
        <div className="flex items-center space-x-6">
          {/* 连接状态 */}
          <div className="flex items-center space-x-1">
            {getConnectionIcon()}
            <span className={`font-medium ${getConnectionColor()}`}>
              {getConnectionText()}
            </span>
          </div>

          {/* 文件选择状态 */}
          <div className="flex items-center space-x-1">
            {selectedCount > 0 ? (
              <CheckCircle className="w-4 h-4 text-blue-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-gray-400" />
            )}
            <span className="text-gray-600">
              已选择 {selectedCount} / {totalFiles} 个文件
            </span>
          </div>

          {/* 当前操作 */}
          {currentOperation && (
            <div className="flex items-center space-x-1">
              <Activity className="w-4 h-4 text-blue-500 animate-spin" />
              <span className="text-blue-600 font-medium">
                {currentOperation}
              </span>
            </div>
          )}
        </div>

        {/* 右侧信息 */}
        <div className="flex items-center space-x-6">
          {/* 系统资源使用情况 */}
          {systemInfo && (
            <>
              <div className="flex items-center space-x-1">
                <Cpu className="w-4 h-4 text-blue-500" />
                <span className="text-gray-600">
                  CPU: {systemInfo.cpu_usage.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <MemoryStick className="w-4 h-4 text-green-500" />
                <span className="text-gray-600">
                  内存: {systemInfo.memory_usage.toFixed(1)}%
                </span>
              </div>
            </>
          )}

          {/* 当前时间 */}
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600 font-mono">
              {formatTime(currentTime)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
