import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { open } from '@tauri-apps/api/dialog';
import { writeText } from '@tauri-apps/api/clipboard';
import { 
  Menu, 
  File, 
  Settings, 
  HelpCircle, 
  Info, 
  FolderOpen, 
  Save, 
  Copy,
  ExternalLink,
  Github,
  MessageCircle
} from 'lucide-react';

interface MenuBarProps {
  onShowAbout: () => void;
  onShowSettings: () => void;
  onShowHelp: () => void;
  onLogout?: () => void;
}

export const MenuBar: React.FC<MenuBarProps> = ({
  onShowAbout,
  onShowSettings,
  onShowHelp,
  onLogout
}) => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const handleMenuClick = (menuName: string) => {
    setActiveMenu(activeMenu === menuName ? null : menuName);
  };

  const handleMenuItemClick = async (action: string) => {
    setActiveMenu(null);
    
    switch (action) {
      case 'open-folder':
        try {
          const selected = await open({
            directory: true,
            title: '选择下载目录'
          });
          if (selected) {
            console.log('选择的目录:', selected);
          }
        } catch (error) {
          console.error('打开文件夹失败:', error);
        }
        break;
        
      case 'save-config':
        try {
          await invoke('save_config', {
            config: {
              last_save: new Date().toISOString(),
              version: '1.0.0'
            }
          });
          console.log('配置已保存');
        } catch (error) {
          console.error('保存配置失败:', error);
        }
        break;
        
      case 'copy-logs':
        try {
          await writeText('日志内容已复制到剪贴板');
          console.log('日志已复制');
        } catch (error) {
          console.error('复制失败:', error);
        }
        break;
        
      case 'github':
        window.open('https://github.com', '_blank');
        break;
        
      case 'feedback':
        window.open('mailto:<EMAIL>', '_blank');
        break;
        
      case 'settings':
        onShowSettings();
        break;
        
      case 'help':
        onShowHelp();
        break;
        
      case 'about':
        onShowAbout();
        break;
        
      default:
        console.log('未知操作:', action);
    }
  };

  const closeMenu = () => {
    setActiveMenu(null);
  };

  return (
    <>
      {/* 菜单栏 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center px-4 py-2 space-x-1">
          {/* 文件菜单 */}
          <div className="relative">
            <button
              onClick={() => handleMenuClick('file')}
              className={`px-3 py-1 text-sm rounded hover:bg-gray-100 flex items-center ${
                activeMenu === 'file' ? 'bg-gray-100' : ''
              }`}
            >
              <File className="w-4 h-4 mr-1" />
              文件
            </button>
            {activeMenu === 'file' && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => handleMenuItemClick('open-folder')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <FolderOpen className="w-4 h-4 mr-2" />
                    打开下载目录
                  </button>
                  <button
                    onClick={() => handleMenuItemClick('save-config')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    保存配置
                  </button>
                  <hr className="my-1" />
                  <button
                    onClick={() => handleMenuItemClick('copy-logs')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    复制日志
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 编辑菜单 */}
          <div className="relative">
            <button
              onClick={() => handleMenuClick('edit')}
              className={`px-3 py-1 text-sm rounded hover:bg-gray-100 flex items-center ${
                activeMenu === 'edit' ? 'bg-gray-100' : ''
              }`}
            >
              <Settings className="w-4 h-4 mr-1" />
              编辑
            </button>
            {activeMenu === 'edit' && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => handleMenuItemClick('settings')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    首选项
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 视图菜单 */}
          <div className="relative">
            <button
              onClick={() => handleMenuClick('view')}
              className={`px-3 py-1 text-sm rounded hover:bg-gray-100 flex items-center ${
                activeMenu === 'view' ? 'bg-gray-100' : ''
              }`}
            >
              <Menu className="w-4 h-4 mr-1" />
              视图
            </button>
            {activeMenu === 'view' && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => handleMenuItemClick('refresh')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                  >
                    刷新
                  </button>
                  <button
                    onClick={() => handleMenuItemClick('fullscreen')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
                  >
                    全屏模式
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 帮助菜单 */}
          <div className="relative">
            <button
              onClick={() => handleMenuClick('help')}
              className={`px-3 py-1 text-sm rounded hover:bg-gray-100 flex items-center ${
                activeMenu === 'help' ? 'bg-gray-100' : ''
              }`}
            >
              <HelpCircle className="w-4 h-4 mr-1" />
              帮助
            </button>
            {activeMenu === 'help' && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => handleMenuItemClick('help')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <HelpCircle className="w-4 h-4 mr-2" />
                    使用帮助
                  </button>
                  <button
                    onClick={() => handleMenuItemClick('github')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <Github className="w-4 h-4 mr-2" />
                    GitHub
                    <ExternalLink className="w-3 h-3 ml-auto" />
                  </button>
                  <button
                    onClick={() => handleMenuItemClick('feedback')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    反馈建议
                    <ExternalLink className="w-3 h-3 ml-auto" />
                  </button>
                  <hr className="my-1" />
                  <button
                    onClick={() => handleMenuItemClick('about')}
                    className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
                  >
                    <Info className="w-4 h-4 mr-2" />
                    关于
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 点击其他地方关闭菜单 */}
      {activeMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={closeMenu}
        />
      )}
    </>
  );
};
