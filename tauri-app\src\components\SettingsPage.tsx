import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { open } from '@tauri-apps/api/dialog';
import { 
  Settings, 
  FolderOpen, 
  Save, 
  RotateCcw,
  Download,
  Bell,
  Shield,
  Monitor
} from 'lucide-react';

interface SettingsData {
  downloadPath: string;
  autoStart: boolean;
  notifications: boolean;
  idmPath: string;
  maxConcurrent: number;
  autoSave: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<SettingsData>({
    downloadPath: 'C:\\Downloads',
    autoStart: false,
    notifications: true,
    idmPath: 'C:\\Program Files (x86)\\Internet Download Manager\\IDMan.exe',
    maxConcurrent: 3,
    autoSave: true,
    theme: 'light'
  });
  
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const config = await invoke('load_config');
      if (config && typeof config === 'object') {
        setSettings(prev => ({ ...prev, ...config }));
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      await invoke('save_config', { config: settings });
      setSaved(true);
      setTimeout(() => setSaved(false), 2000);
    } catch (error) {
      console.error('保存设置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  const selectDownloadPath = async () => {
    try {
      const selected = await open({
        directory: true,
        title: '选择下载目录'
      });
      if (selected && typeof selected === 'string') {
        setSettings(prev => ({ ...prev, downloadPath: selected }));
      }
    } catch (error) {
      console.error('选择目录失败:', error);
    }
  };

  const selectIdmPath = async () => {
    try {
      const selected = await open({
        filters: [{
          name: 'IDM可执行文件',
          extensions: ['exe']
        }],
        title: '选择IDM程序路径'
      });
      if (selected && typeof selected === 'string') {
        setSettings(prev => ({ ...prev, idmPath: selected }));
      }
    } catch (error) {
      console.error('选择IDM路径失败:', error);
    }
  };

  const resetSettings = () => {
    setSettings({
      downloadPath: 'C:\\Downloads',
      autoStart: false,
      notifications: true,
      idmPath: 'C:\\Program Files (x86)\\Internet Download Manager\\IDMan.exe',
      maxConcurrent: 3,
      autoSave: true,
      theme: 'light'
    });
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
          <Settings className="w-6 h-6 mr-2" />
          设置
        </h1>
        <div className="flex space-x-2">
          <button
            onClick={resetSettings}
            className="btn-outline text-sm flex items-center space-x-1"
          >
            <RotateCcw className="w-4 h-4" />
            <span>重置</span>
          </button>
          <button
            onClick={saveSettings}
            disabled={saving}
            className="btn-primary text-sm flex items-center space-x-1"
          >
            <Save className="w-4 h-4" />
            <span>{saving ? '保存中...' : saved ? '已保存' : '保存'}</span>
          </button>
        </div>
      </div>

      {/* 下载设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Download className="w-5 h-5 mr-2" />
          下载设置
        </h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              默认下载目录
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={settings.downloadPath}
                onChange={(e) => setSettings(prev => ({ ...prev, downloadPath: e.target.value }))}
                className="input flex-1"
                placeholder="选择下载目录"
              />
              <button
                onClick={selectDownloadPath}
                className="btn-outline flex items-center space-x-1"
              >
                <FolderOpen className="w-4 h-4" />
                <span>浏览</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              IDM程序路径
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={settings.idmPath}
                onChange={(e) => setSettings(prev => ({ ...prev, idmPath: e.target.value }))}
                className="input flex-1"
                placeholder="IDM程序路径"
              />
              <button
                onClick={selectIdmPath}
                className="btn-outline flex items-center space-x-1"
              >
                <FolderOpen className="w-4 h-4" />
                <span>浏览</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最大并发下载数
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={settings.maxConcurrent}
              onChange={(e) => setSettings(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) || 3 }))}
              className="input w-32"
            />
            <p className="text-xs text-gray-500 mt-1">建议设置为1-5之间</p>
          </div>
        </div>
      </div>

      {/* 应用设置 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Monitor className="w-5 h-5 mr-2" />
          应用设置
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">开机自启动</label>
              <p className="text-xs text-gray-500">程序将在系统启动时自动运行</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoStart}
                onChange={(e) => setSettings(prev => ({ ...prev, autoStart: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">桌面通知</label>
              <p className="text-xs text-gray-500">下载完成时显示系统通知</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications}
                onChange={(e) => setSettings(prev => ({ ...prev, notifications: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">自动保存设置</label>
              <p className="text-xs text-gray-500">更改设置时自动保存</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoSave}
                onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              主题设置
            </label>
            <select
              value={settings.theme}
              onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as 'light' | 'dark' | 'auto' }))}
              className="input w-40"
            >
              <option value="light">浅色</option>
              <option value="dark">深色</option>
              <option value="auto">跟随系统</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};
