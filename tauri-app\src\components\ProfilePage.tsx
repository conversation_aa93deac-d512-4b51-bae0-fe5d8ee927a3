import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  User, 
  Shield, 
  Calendar, 
  Database, 
  HardDrive, 
  Clock,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

interface ProfilePageProps {
  token: string;
  tokenData: TokenData | null;
}

export const ProfilePage: React.FC<ProfilePageProps> = ({ token, tokenData }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [currentData, setCurrentData] = useState<TokenData | null>(tokenData);

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '永久有效';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const refreshTokenInfo = async () => {
    setRefreshing(true);
    try {
      const response = await invoke('validate_token', { token });
      if (response.code === 200 && response.data) {
        setCurrentData(response.data);
      }
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getUsagePercentage = (remaining: number, total: number) => {
    return (remaining / total * 100);
  };

  const getUsedPercentage = (remaining: number, total: number) => {
    return ((total - remaining) / total * 100);
  };

  const getStatusColor = (percentage: number) => {
    if (percentage > 50) return 'text-green-600';
    if (percentage > 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage > 50) return 'bg-green-500';
    if (percentage > 20) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  useEffect(() => {
    setCurrentData(tokenData);
  }, [tokenData]);

  if (!currentData) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">无法获取账户信息</p>
        </div>
      </div>
    );
  }

  const countRemaining = getUsagePercentage(currentData.remaining_count, currentData.count);
  const sizeRemaining = getUsagePercentage(currentData.remaining_size, currentData.size);
  const countUsed = getUsedPercentage(currentData.remaining_count, currentData.count);
  const sizeUsed = getUsedPercentage(currentData.remaining_size, currentData.size);

  return (
    <div className="p-6 space-y-6">
      {/* 账户概览 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <User className="w-6 h-6 mr-2" />
            账户信息
          </h2>
          <button
            onClick={refreshTokenInfo}
            disabled={refreshing}
            className="btn-outline text-sm flex items-center space-x-1"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-blue-500 mr-2" />
                <span className="text-sm font-medium">账户状态</span>
              </div>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm font-bold text-green-600">有效</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-purple-500 mr-2" />
                <span className="text-sm font-medium">到期时间</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {formatDate(currentData.expires_at)}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Clock className="w-5 h-5 text-orange-500 mr-2" />
                <span className="text-sm font-medium">最后使用</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {formatDate(currentData.used_at)}
              </span>
            </div>
          </div>

          {/* 卡密信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">卡密信息</h3>
            <div className="text-xs text-gray-600 font-mono break-all">
              {currentData.token}
            </div>
          </div>
        </div>
      </div>

      {/* 使用统计 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
          <Database className="w-6 h-6 mr-2" />
          使用统计
        </h2>

        <div className="space-y-6">
          {/* 次数统计 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Database className="w-5 h-5 text-blue-500 mr-2" />
                <span className="text-sm font-medium">剩余次数</span>
              </div>
              <span className={`text-sm font-bold ${getStatusColor(countRemaining)}`}>
                {currentData.remaining_count.toLocaleString()} / {currentData.count.toLocaleString()}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(countRemaining)}`}
                style={{ width: `${Math.max(countRemaining, 5)}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              剩余 {countRemaining.toFixed(1)}% (已使用 {countUsed.toFixed(1)}%)
            </div>
          </div>

          {/* 流量统计 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <HardDrive className="w-5 h-5 text-green-500 mr-2" />
                <span className="text-sm font-medium">剩余流量</span>
              </div>
              <span className={`text-sm font-bold ${getStatusColor(sizeRemaining)}`}>
                {formatBytes(currentData.remaining_size)} / {formatBytes(currentData.size)}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(sizeRemaining)}`}
                style={{ width: `${Math.max(sizeRemaining, 5)}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              剩余 {sizeRemaining.toFixed(1)}% (已使用 {sizeUsed.toFixed(1)}%)
            </div>
          </div>
        </div>
      </div>

      {/* 使用建议 */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">使用建议</h3>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• 建议在剩余次数低于 20% 时及时续费</li>
          <li>• 大文件下载会消耗较多流量，请合理安排</li>
          <li>• 定期检查账户状态，确保服务正常</li>
          <li>• 如有异常请及时联系客服处理</li>
        </ul>
      </div>
    </div>
  );
};
