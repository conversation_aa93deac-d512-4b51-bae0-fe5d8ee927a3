{"rustc": 16591470773350601817, "features": "[\"bytes\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"default\", \"dialog\", \"dialog-ask\", \"dialog-open\", \"dialog-save\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"regex\", \"reqwest\", \"rfd\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 5354242864495067279, "deps": [[40386456601120721, "percent_encoding", false, 3754651263818111578], [947818755262499932, "notify_rust", false, 4604797395582005206], [1260461579271933187, "serialize_to_javascript", false, 13203032112853061896], [1441306149310335789, "tempfile", false, 15764044179293174694], [3150220818285335163, "url", false, 9721221189520161498], [3722963349756955755, "once_cell", false, 7160926443655400854], [3988549704697787137, "open", false, 4709196717310091145], [4381063397040571828, "webview2_com", false, 11290250088670576088], [4405182208873388884, "http", false, 5574349850812844607], [4450062412064442726, "dirs_next", false, 13158494868361667006], [4899080583175475170, "semver", false, 3963461093089149132], [5099504066399492044, "rfd", false, 8037699616368841477], [5180608563399064494, "tauri_macros", false, 14244644369857535108], [5610773616282026064, "build_script_build", false, 5206952670647209026], [5986029879202738730, "log", false, 9338200190250501583], [7244058819997729774, "reqwest", false, 13015047477193966496], [7653476968652377684, "windows", false, 10690132649445514100], [8008191657135824715, "thiserror", false, 10567539360728756001], [8292277814562636972, "tauri_utils", false, 17068750469945825521], [8319709847752024821, "uuid", false, 17329545268604777101], [9451456094439810778, "regex", false, 12637163302884941634], [9538054652646069845, "tokio", false, 13526358452819035142], [9623796893764309825, "ignore", false, 1208364540863738141], [9689903380558560274, "serde", false, 3990983680525661946], [9920160576179037441, "getrandom", false, 8222686775362201374], [10563170702865159712, "flate2", false, 8397138680430382723], [10629569228670356391, "futures_util", false, 9605774525482233814], [11601763207901161556, "tar", false, 8900053092451091088], [11693073011723388840, "raw_window_handle", false, 14717548697597070664], [11989259058781683633, "dunce", false, 11188309730602775730], [12986574360607194341, "serde_repr", false, 16490300182119299344], [13208667028893622512, "rand", false, 13122419021854910965], [13625485746686963219, "anyhow", false, 16646634681217262864], [14162324460024849578, "tauri_runtime", false, 12444769989192652711], [14564311161534545801, "encoding_rs", false, 3965033405047791285], [14923790796823607459, "indexmap", false, 8192397723110361000], [15367738274754116744, "serde_json", false, 16765817478399170525], [16066129441945555748, "bytes", false, 12759223928222694002], [16228250612241359704, "tauri_runtime_wry", false, 14998335249518297578], [17155886227862585100, "glob", false, 4480380036398234173], [17278893514130263345, "state", false, 14947730162666122960]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-1f61742c44a681dd\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}