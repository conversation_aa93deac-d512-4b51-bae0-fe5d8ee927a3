import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { Shield, User, Calendar, Crown, CheckCircle, XCircle, RefreshCw, Database, Clock, HardDrive } from 'lucide-react';

interface LicenseInfo {
  token: string;
  expires_at?: string;
  user_id?: string;
  plan: string;
  status: string;
}

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

interface LicenseInfoProps {
  token: string;
  tokenData?: TokenData;
}

export const LicenseInfo: React.FC<LicenseInfoProps> = ({ token, tokenData }) => {
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo | null>(null);
  const [loading, setLoading] = useState(true);

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const fetchLicenseInfo = async () => {
    if (!token) {
      setLicenseInfo(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const info: LicenseInfo = await invoke('get_license_info', { token });
      setLicenseInfo(info);
    } catch (error) {
      console.error('获取授权信息失败:', error);
      setLicenseInfo(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLicenseInfo();
  }, [token]);

  const getStatusIcon = (status: string) => {
    if (status === '有效') {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    if (status === '有效') {
      return 'text-green-600 bg-green-50 border-green-200';
    } else {
      return 'text-red-600 bg-red-50 border-red-200';
    }
  };

  const getPlanIcon = (plan: string) => {
    if (plan.includes('高级') || plan.includes('专业')) {
      return <Crown className="w-4 h-4 text-yellow-500" />;
    } else {
      return <Shield className="w-4 h-4 text-blue-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const getDaysUntilExpiry = (expiryDate?: string) => {
    if (!expiryDate) return null;
    try {
      const expiry = new Date(expiryDate);
      const now = new Date();
      const diffTime = expiry.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch {
      return null;
    }
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            授权信息
          </h3>
        </div>
        <div className="card-content">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            授权信息
          </h3>
        </div>
        <div className="card-content">
          <div className="text-center py-4">
            <XCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">请输入解析卡密</p>
          </div>
        </div>
      </div>
    );
  }

  if (!licenseInfo) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            授权信息
          </h3>
        </div>
        <div className="card-content">
          <div className="text-center py-4">
            <XCircle className="w-12 h-12 text-red-400 mx-auto mb-2" />
            <p className="text-red-500">无法获取授权信息</p>
            <button
              onClick={fetchLicenseInfo}
              className="mt-2 btn-outline text-sm"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  const daysUntilExpiry = getDaysUntilExpiry(licenseInfo.expires_at);

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          授权信息
          <button
            onClick={fetchLicenseInfo}
            className="ml-auto p-1 hover:bg-gray-100 rounded"
            title="刷新授权信息"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </h3>
      </div>
      <div className="card-content space-y-4">
        {/* 授权状态 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {getStatusIcon(licenseInfo.status)}
            <span className="text-sm font-medium ml-2">授权状态</span>
          </div>
          <span className={`text-sm font-bold px-2 py-1 rounded border ${getStatusColor(licenseInfo.status)}`}>
            {licenseInfo.status}
          </span>
        </div>

        {/* 套餐类型 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {getPlanIcon(licenseInfo.plan)}
            <span className="text-sm font-medium ml-2">套餐类型</span>
          </div>
          <span className="text-sm font-bold text-gray-700">
            {licenseInfo.plan}
          </span>
        </div>

        {/* 用户ID */}
        {licenseInfo.user_id && (
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium ml-2">用户ID</span>
            </div>
            <span className="text-sm font-mono text-gray-600">
              {licenseInfo.user_id}
            </span>
          </div>
        )}

        {/* 到期时间 */}
        {licenseInfo.expires_at && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 text-blue-500" />
                <span className="text-sm font-medium ml-2">到期时间</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {formatDate(licenseInfo.expires_at)}
              </span>
            </div>
            {daysUntilExpiry !== null && (
              <div className="text-xs text-center">
                {daysUntilExpiry > 0 ? (
                  <span className={`px-2 py-1 rounded ${
                    daysUntilExpiry > 30 ? 'bg-green-100 text-green-700' :
                    daysUntilExpiry > 7 ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    还有 {daysUntilExpiry} 天到期
                  </span>
                ) : (
                  <span className="px-2 py-1 rounded bg-red-100 text-red-700">
                    已过期 {Math.abs(daysUntilExpiry)} 天
                  </span>
                )}
              </div>
            )}
          </div>
        )}

        {/* 详细统计信息 */}
        {tokenData && (
          <div className="pt-4 border-t border-gray-200 space-y-3">
            <h4 className="text-sm font-semibold text-gray-900">使用统计</h4>

            {/* 剩余次数 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Database className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium ml-2">剩余次数</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {tokenData.remaining_count.toLocaleString()} / {tokenData.count.toLocaleString()}
              </span>
            </div>

            {/* 剩余流量 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <HardDrive className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium ml-2">剩余流量</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {formatBytes(tokenData.remaining_size)} / {formatBytes(tokenData.size)}
              </span>
            </div>

            {/* 最后使用时间 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Clock className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium ml-2">最后使用</span>
              </div>
              <span className="text-sm font-bold text-gray-700">
                {formatDate(tokenData.used_at)}
              </span>
            </div>

            {/* 使用进度条 */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500">
                <span>次数使用率</span>
                <span>{((tokenData.count - tokenData.remaining_count) / tokenData.count * 100).toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(tokenData.count - tokenData.remaining_count) / tokenData.count * 100}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500">
                <span>流量使用率</span>
                <span>{((tokenData.size - tokenData.remaining_size) / tokenData.size * 100).toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(tokenData.size - tokenData.remaining_size) / tokenData.size * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* 卡密信息 */}
        <div className="pt-2 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            <div className="flex items-center justify-between">
              <span>卡密:</span>
              <span className="font-mono">
                {licenseInfo.token.length > 16
                  ? `${licenseInfo.token.substring(0, 8)}...${licenseInfo.token.substring(licenseInfo.token.length - 8)}`
                  : licenseInfo.token
                }
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
