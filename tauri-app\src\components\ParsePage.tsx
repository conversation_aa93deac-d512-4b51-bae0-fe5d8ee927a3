import React, { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Download, 
  ArrowLeft, 
  Play,
  CheckSquare,
  Square,
  Copy,
  RefreshCw,
  Folder,
  File
} from 'lucide-react';
import { FileList } from './FileList';

interface FileInfo {
  fs_id: number;
  path: string;
  server_filename: string;
  size: number;
  isdir: number;
  server_mtime: number;
}

interface ApiResponse {
  code: number;
  message: string;
  data?: {
    uk?: number;
    shareid?: number;
    randsk?: string;
    list: FileInfo[];
  };
}

interface ParsePageProps {
  token: string;
  onLog: (message: string) => void;
}

export const ParsePage: React.FC<ParsePageProps> = ({ token, onLog }) => {
  const [shareUrl, setShareUrl] = useState('https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?');
  const [password, setPassword] = useState('1111');
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Set<number>>(new Set());

  const addLog = useCallback((message: string) => {
    onLog(`[${new Date().toLocaleTimeString()}] ${message}`);
  }, [onLog]);

  const getFileList = async () => {
    if (!shareUrl || !password || !token) {
      addLog('请填写完整的分享链接、提取码和解析卡密');
      return;
    }

    setLoading(true);
    addLog(`开始获取文件列表... 路径: ${currentPath}`);

    try {
      const response: ApiResponse = await invoke('get_file_list', {
        token,
        url: shareUrl,
        pwd: password,
        dir: currentPath,
      });

      if (response.code === 200 && response.data) {
        setFiles(response.data.list);
        addLog(`成功获取 ${response.data.list.length} 个文件/文件夹`);
      } else {
        addLog(`获取文件列表失败: ${response.message}`);
      }
    } catch (error) {
      addLog(`获取文件列表出错: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (fsId: number, selected: boolean) => {
    const newSelected = new Set(selectedFiles);
    if (selected) {
      newSelected.add(fsId);
    } else {
      newSelected.delete(fsId);
    }
    setSelectedFiles(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedFiles.size === files.length) {
      setSelectedFiles(new Set());
    } else {
      setSelectedFiles(new Set(files.map(f => f.fs_id)));
    }
  };

  const handleFolderClick = (folderPath: string) => {
    setCurrentPath(folderPath);
    setSelectedFiles(new Set());
  };

  const handleBackClick = () => {
    const pathParts = currentPath.split('/').filter(p => p);
    pathParts.pop();
    const newPath = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
    setCurrentPath(newPath);
    setSelectedFiles(new Set());
  };

  const downloadSelected = async () => {
    if (selectedFiles.size === 0) {
      addLog('请先选择要下载的文件');
      return;
    }

    const selectedFsIds = Array.from(selectedFiles);
    addLog(`开始获取 ${selectedFsIds.length} 个文件的下载链接...`);

    try {
      const response = await invoke('get_download_links', {
        token,
        url: shareUrl,
        pwd: password,
        dir: currentPath,
        fsIds: selectedFsIds,
      });

      addLog(`成功获取下载链接，准备调用IDM下载...`);
      
      const success = await invoke('call_idm_download', {
        response: JSON.stringify(response)
      });

      if (success) {
        addLog('IDM下载任务已创建');
      } else {
        addLog('IDM调用失败，请检查IDM是否正确安装');
      }
    } catch (error) {
      addLog(`下载失败: ${error}`);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* 输入区域 */}
      <div className="bg-white p-6 border-b border-gray-200">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分享链接
              </label>
              <input
                type="text"
                value={shareUrl}
                onChange={(e) => setShareUrl(e.target.value)}
                className="input"
                placeholder="请输入百度网盘分享链接"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                提取码
              </label>
              <input
                type="text"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="input"
                placeholder="请输入提取码"
              />
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={getFileList}
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{loading ? '获取中...' : '获取文件列表'}</span>
            </button>
            
            {files.length > 0 && (
              <button
                onClick={downloadSelected}
                disabled={selectedFiles.size === 0}
                className="btn-secondary flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>下载选中 ({selectedFiles.size})</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 文件列表区域 */}
      <div className="flex-1 overflow-hidden">
        {files.length > 0 && (
          <div className="h-full flex flex-col">
            {/* 路径导航和操作栏 */}
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {currentPath !== '/' && (
                  <button
                    onClick={handleBackClick}
                    className="btn-outline text-sm"
                  >
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    返回上级
                  </button>
                )}
                <span className="text-sm text-gray-600">
                  当前路径: {currentPath}
                </span>
              </div>
              
              <button
                onClick={handleSelectAll}
                className="btn-outline text-sm flex items-center space-x-1"
              >
                {selectedFiles.size === files.length ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
                <span>全选</span>
              </button>
            </div>

            {/* 文件列表 */}
            <div className="flex-1 overflow-auto">
              <FileList
                files={files}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onFolderClick={handleFolderClick}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
