import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import {
  Shield,
  Key,
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Globe,
  Lock,
  Wifi,
  RefreshCw
} from 'lucide-react';

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

interface TokenValidationResponse {
  code: number;
  message: string;
  data?: TokenData;
}

interface NetworkStatus {
  api_reachable: boolean;
  response_time: number;
  error_message?: string;
}

interface LoginScreenProps {
  onLoginSuccess: (token: string, tokenData: TokenData) => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showToken, setShowToken] = useState(false);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus | null>(null);
  const [checkingNetwork, setCheckingNetwork] = useState(false);

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '永久有效';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const handleValidateToken = async () => {
    if (!token.trim()) {
      setError('请输入解析卡密');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // 添加一个最小延迟以显示加载动画
      const [response] = await Promise.all([
        invoke('validate_token', { token: token.trim() }),
        new Promise(resolve => setTimeout(resolve, 1000)) // 最少显示1秒加载动画
      ]);

      if (response.code === 200 && response.data) {
        // 验证成功
        onLoginSuccess(token.trim(), response.data);
      } else {
        setError(response.message || '卡密验证失败');
      }
    } catch (err) {
      setError('网络连接失败，请检查网络后重试');
      console.error('Token validation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleValidateToken();
    }
  };

  const checkNetwork = async () => {
    setCheckingNetwork(true);
    setError('');

    try {
      const status: NetworkStatus = await invoke('check_network');
      setNetworkStatus(status);

      if (!status.api_reachable) {
        setError(`网络连接失败: ${status.error_message || '无法连接到服务器'}`);
      }
    } catch (err) {
      console.error('网络检测失败:', err);
      setError('网络检测失败，请检查网络连接');
      setNetworkStatus({
        api_reachable: false,
        response_time: 0,
        error_message: '检测失败'
      });
    } finally {
      setCheckingNetwork(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="max-w-sm w-full">
        {/* 简洁登录卡片 */}
        <div className="bg-white rounded-lg shadow-md p-6 relative">
          {/* 加载遮罩 */}
          {loading && (
            <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center rounded-lg z-10">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">验证中，请稍候...</p>
              </div>
            </div>
          )}
          <div className="space-y-4">
            {/* 标题 */}
            <div className="text-center">
              <h2 className="text-lg font-medium text-gray-900">身份验证</h2>
              <p className="text-sm text-gray-500 mt-1">请输入访问密钥</p>
            </div>

            {/* 卡密输入 */}
            <div>
              <input
                type={showToken ? 'text' : 'password'}
                value={token}
                onChange={(e) => setToken(e.target.value)}
                onKeyPress={handleKeyPress}
                className="input"
                placeholder="请输入密钥"
                disabled={loading}
              />
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="text-red-600 bg-red-50 p-2 rounded text-sm text-center">
                {error}
              </div>
            )}

            {/* 网络检测按钮 */}
            <button
              onClick={checkNetwork}
              disabled={checkingNetwork}
              className="w-full btn-secondary flex items-center justify-center space-x-2"
            >
              {checkingNetwork ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>检测中...</span>
                </>
              ) : (
                <>
                  <Wifi className="w-4 h-4" />
                  <span>网络检测</span>
                </>
              )}
            </button>

            {/* 网络状态显示 */}
            {networkStatus && (
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                networkStatus.api_reachable
                  ? 'bg-green-50 text-green-700'
                  : 'bg-red-50 text-red-700'
              }`}>
                {networkStatus.api_reachable ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                <div className="flex-1">
                  <span className="text-sm font-medium">
                    {networkStatus.api_reachable ? '网络连接正常' : '网络连接失败'}
                  </span>
                  {networkStatus.api_reachable && (
                    <span className="text-xs ml-2">
                      响应时间: {networkStatus.response_time}ms
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* 验证按钮 */}
            <button
              onClick={handleValidateToken}
              disabled={loading || !token.trim()}
              className="w-full btn-primary flex items-center justify-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>验证中...</span>
                </>
              ) : (
                <span>验证</span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
