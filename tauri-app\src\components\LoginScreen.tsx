import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import {
  Shield,
  Key,
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Globe,
  Lock,
  Wifi,
  RefreshCw
} from 'lucide-react';

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  length: number;
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

interface TokenValidationResponse {
  code: number;
  message: string;
  data?: TokenData;
}

interface NetworkStatus {
  api_reachable: boolean;
  response_time: number;
  error_message?: string;
}

interface LoginScreenProps {
  onLoginSuccess: (token: string, tokenData: TokenData) => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showToken, setShowToken] = useState(false);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus | null>(null);
  const [checkingNetwork, setCheckingNetwork] = useState(false);

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '永久有效';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  const handleValidateToken = async () => {
    if (!token.trim()) {
      setError('请输入解析卡密');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response: TokenValidationResponse = await invoke('validate_token', { 
        token: token.trim() 
      });

      if (response.code === 200 && response.data) {
        // 验证成功
        onLoginSuccess(token.trim(), response.data);
      } else {
        setError(response.message || '卡密验证失败');
      }
    } catch (err) {
      setError('网络连接失败，请检查网络后重试');
      console.error('Token validation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleValidateToken();
    }
  };

  const checkNetwork = async () => {
    setCheckingNetwork(true);
    setError('');

    try {
      const status: NetworkStatus = await invoke('check_network');
      setNetworkStatus(status);

      if (!status.api_reachable) {
        setError(`网络连接失败: ${status.error_message || '无法连接到服务器'}`);
      }
    } catch (err) {
      console.error('网络检测失败:', err);
      setError('网络检测失败，请检查网络连接');
      setNetworkStatus({
        api_reachable: false,
        response_time: 0,
        error_message: '检测失败'
      });
    } finally {
      setCheckingNetwork(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo 和标题 */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Globe className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">百度网盘解析工具</h1>
          <p className="text-gray-600">请输入您的解析卡密以继续使用</p>
        </div>

        {/* 登录卡片 */}
        <div className="bg-white rounded-xl shadow-xl p-8">
          <div className="space-y-6">
            {/* 卡密输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Key className="w-4 h-4 inline mr-1" />
                解析卡密
              </label>
              <div className="relative">
                <input
                  type={showToken ? 'text' : 'password'}
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="input pr-10"
                  placeholder="请输入您的解析卡密"
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowToken(!showToken)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                <XCircle className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* 网络检测按钮 */}
            <button
              onClick={checkNetwork}
              disabled={checkingNetwork}
              className="w-full btn-secondary flex items-center justify-center space-x-2 h-10"
            >
              {checkingNetwork ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>检测中...</span>
                </>
              ) : (
                <>
                  <Wifi className="w-4 h-4" />
                  <span>检测网络</span>
                </>
              )}
            </button>

            {/* 网络状态显示 */}
            {networkStatus && (
              <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                networkStatus.api_reachable
                  ? 'bg-green-50 text-green-700'
                  : 'bg-red-50 text-red-700'
              }`}>
                {networkStatus.api_reachable ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                <div className="flex-1">
                  <span className="text-sm font-medium">
                    {networkStatus.api_reachable ? '网络连接正常' : '网络连接失败'}
                  </span>
                  {networkStatus.api_reachable && (
                    <span className="text-xs ml-2">
                      响应时间: {networkStatus.response_time}ms
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* 验证按钮 */}
            <button
              onClick={handleValidateToken}
              disabled={loading || !token.trim()}
              className="w-full btn-primary flex items-center justify-center space-x-2 h-12"
            >
              {loading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>验证中...</span>
                </>
              ) : (
                <>
                  <Shield className="w-5 h-5" />
                  <span>验证卡密</span>
                </>
              )}
            </button>

            {/* 说明信息 */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
                <Lock className="w-4 h-4 mr-1" />
                安全说明
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• 您的卡密信息将被安全加密传输</li>
                <li>• 验证成功后可享受高速解析服务</li>
                <li>• 如有问题请联系客服获取帮助</li>
              </ul>
            </div>

            {/* 功能特点 */}
            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">功能特点</h3>
              <div className="grid grid-cols-2 gap-3 text-xs text-gray-600">
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>高速下载</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>批量处理</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>IDM 集成</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>实时监控</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="text-center mt-6 text-sm text-gray-500">
          <p>© 2024 百度网盘解析工具. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};
