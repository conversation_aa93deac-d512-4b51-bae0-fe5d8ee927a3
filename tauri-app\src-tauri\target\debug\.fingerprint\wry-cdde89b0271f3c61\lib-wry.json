{"rustc": 16591470773350601817, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 370650484435432200, "deps": [[2924422107542798392, "libc", false, 14163701268614542639], [3007252114546291461, "tao", false, 13253651350214531958], [3150220818285335163, "url", false, 9721221189520161498], [3540822385484940109, "windows_implement", false, 17578348583083551929], [3722963349756955755, "once_cell", false, 7160926443655400854], [4381063397040571828, "webview2_com", false, 11290250088670576088], [4405182208873388884, "http", false, 5574349850812844607], [5986029879202738730, "log", false, 9338200190250501583], [7653476968652377684, "windows", false, 10690132649445514100], [8008191657135824715, "thiserror", false, 10567539360728756001], [8391357152270261188, "build_script_build", false, 10192024630283994589], [9689903380558560274, "serde", false, 3990983680525661946], [11989259058781683633, "dunce", false, 11188309730602775730], [15367738274754116744, "serde_json", false, 16765817478399170525]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-cdde89b0271f3c61\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}