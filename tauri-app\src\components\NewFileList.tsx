import React, { useState, useRef, useEffect } from 'react';
import { Folder, File, CheckSquare, Square, Download, MoreVertical } from 'lucide-react';

interface FileInfo {
  fs_id: number;
  isdir: number;
  server_filename: string;
  size: number;
  path: string;
  server_mtime: number;
}

interface FileListProps {
  files: FileInfo[];
  selectedFiles: Set<number>;
  onFileSelect: (fsId: number, selected: boolean) => void;
  onFolderClick: (folderPath: string) => void;
  onDownloadFiles: (fileIds: number[]) => void;
}

export const FileList: React.FC<FileListProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onFolderClick,
  onDownloadFiles,
}) => {
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    fileId: number;
    fileName: string;
  } | null>(null);
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number>(-1);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  // 格式化文件大小
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '文件夹';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  };

  // 处理复选框点击
  const handleCheckboxClick = (fsId: number, index: number, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (event.shiftKey && lastSelectedIndex !== -1) {
      // Shift 多选
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);
      
      for (let i = start; i <= end; i++) {
        const file = files[i];
        if (!selectedFiles.has(file.fs_id)) {
          onFileSelect(file.fs_id, true);
        }
      }
    } else {
      // 单选
      const isSelected = selectedFiles.has(fsId);
      onFileSelect(fsId, !isSelected);
      setLastSelectedIndex(index);
    }
  };

  // 处理双击
  const handleDoubleClick = (file: FileInfo) => {
    if (file.isdir === 1) {
      onFolderClick(file.path);
    }
  };

  // 处理右键菜单
  const handleContextMenu = (event: React.MouseEvent, file: FileInfo) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      fileId: file.fs_id,
      fileName: file.server_filename,
    });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // 处理下载
  const handleDownload = (fileIds: number[]) => {
    onDownloadFiles(fileIds);
    closeContextMenu();
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        closeContextMenu();
      }
    };

    if (contextMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [contextMenu]);

  if (files.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <File className="w-12 h-12 mx-auto mb-2 opacity-50" />
        <p>暂无文件</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                选择
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文件名
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                大小
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                修改时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {files.map((file, index) => (
              <tr
                key={file.fs_id}
                className={`hover:bg-gray-50 cursor-pointer ${
                  selectedFiles.has(file.fs_id) ? 'bg-blue-50' : ''
                }`}
                onDoubleClick={() => handleDoubleClick(file)}
                onContextMenu={(e) => handleContextMenu(e, file)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={(e) => handleCheckboxClick(file.fs_id, index, e)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {selectedFiles.has(file.fs_id) ? (
                      <CheckSquare className="w-5 h-5 text-blue-600" />
                    ) : (
                      <Square className="w-5 h-5" />
                    )}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {file.isdir === 1 ? (
                      <Folder className="w-5 h-5 text-blue-500 mr-2" />
                    ) : (
                      <File className="w-5 h-5 text-gray-400 mr-2" />
                    )}
                    <span className={`text-sm font-medium ${
                      file.isdir === 1 ? 'text-blue-600' : 'text-gray-900'
                    }`}>
                      {file.server_filename}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatSize(file.size)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatTime(file.server_mtime)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleContextMenu(e, file);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed bg-white border border-gray-200 rounded-md shadow-lg z-50 py-1"
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
          }}
        >
          <button
            onClick={() => handleDownload([contextMenu.fileId])}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
          >
            <Download className="w-4 h-4 mr-2" />
            下载
          </button>
          {selectedFiles.size > 1 && selectedFiles.has(contextMenu.fileId) && (
            <button
              onClick={() => handleDownload(Array.from(selectedFiles))}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center"
            >
              <Download className="w-4 h-4 mr-2" />
              下载选中的 {selectedFiles.size} 个文件
            </button>
          )}
        </div>
      )}
    </div>
  );
};
