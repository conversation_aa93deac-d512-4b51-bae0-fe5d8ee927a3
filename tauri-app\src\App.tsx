import React, { useState, useCallback, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import {
  Download,
  Folder,
  File,
  ArrowLeft,
  Settings,
  Play,
  CheckSquare,
  Square,
  Copy,
  RefreshCw,
  Minimize2,
  Maximize2,
  X
} from 'lucide-react';
import { FileList } from './components/FileList';
import { SettingsPanel } from './components/SettingsPanel';
import { LogPanel } from './components/LogPanel';
import { MenuBar } from './components/MenuBar';
import { StatusBar } from './components/StatusBar';
import { SystemInfo } from './components/SystemInfo';
import { LicenseInfo } from './components/LicenseInfo';
import { AboutDialog } from './components/AboutDialog';
import { LoginScreen } from './components/LoginScreen';
import { NetworkCheck } from './components/NetworkCheck';
import { NetworkTest } from './components/NetworkTest';
import { Sidebar } from './components/Sidebar';
import { ParsePage } from './components/ParsePage';
import { ProfilePage } from './components/ProfilePage';
import { SettingsPage } from './components/SettingsPage';

interface FileInfo {
  fs_id: number;
  is_dir: boolean;
  server_filename: string;
  size: number;
  path: string;
  server_mtime: number;
  selected?: boolean;
}

interface ApiResponse {
  code: number;
  message: string;
  data?: {
    uk?: number;
    shareid?: number;
    randsk?: string;
    list: FileInfo[];
  };
}

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

function App() {
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [tokenData, setTokenData] = useState<TokenData | null>(null);

  // 应用状态
  const [shareUrl, setShareUrl] = useState('https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?');
  const [password, setPassword] = useState('1111');
  const [token, setToken] = useState('');
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Set<number>>(new Set());
  const [showAbout, setShowAbout] = useState(false);
  const [activeTab, setActiveTab] = useState('parse');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting' | 'checking'>('disconnected');
  const [currentOperation, setCurrentOperation] = useState<string | undefined>();

  // 处理登录成功
  const handleLoginSuccess = (validatedToken: string, data: TokenData) => {
    setToken(validatedToken);
    setTokenData(data);
    setIsLoggedIn(true);
    addLog(`登录成功！欢迎使用，剩余次数: ${data.remaining_count.toLocaleString()}`);
  };

  // 处理登出
  const handleLogout = () => {
    setIsLoggedIn(false);
    setToken('');
    setTokenData(null);
    setFiles([]);
    setSelectedFiles(new Set());
    setCurrentPath('/');
    setConnectionStatus('disconnected');
    setLogs([]);
  };

  // 加载配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await invoke('load_config');
        console.log('加载的配置:', config);

        // 如果有保存的token，尝试自动验证
        if (config.token) {
          try {
            const response = await invoke('validate_token', { token: config.token });
            if (response.code === 200 && response.data) {
              handleLoginSuccess(config.token, response.data);
            }
          } catch (error) {
            console.log('自动登录失败:', error);
          }
        }
      } catch (error) {
        console.error('加载配置失败:', error);
      }
    };
    loadConfig();
  }, []);

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  const getFileList = async () => {
    if (!shareUrl || !password || !token) {
      addLog('请填写完整的分享链接、提取码和解析卡密');
      return;
    }

    setLoading(true);
    setConnectionStatus('connecting');
    setCurrentOperation('获取文件列表...');
    addLog(`开始获取文件列表... 路径: ${currentPath}`);

    try {
      const response: ApiResponse = await invoke('get_file_list', {
        token,
        url: shareUrl,
        pwd: password,
        dir: currentPath,
      });

      if (response.code === 200 && response.data) {
        setFiles(response.data.list);
        setConnectionStatus('connected');
        addLog(`成功获取 ${response.data.list.length} 个文件/文件夹`);
      } else {
        setConnectionStatus('disconnected');
        addLog(`获取文件列表失败: ${response.message}`);
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      addLog(`获取文件列表出错: ${error}`);
    } finally {
      setLoading(false);
      setCurrentOperation(undefined);
    }
  };

  const enterFolder = (folderPath: string) => {
    setCurrentPath(folderPath);
    setSelectedFiles(new Set());
  };

  const goBack = () => {
    if (currentPath === '/') return;
    
    const pathParts = currentPath.split('/').filter(Boolean);
    pathParts.pop();
    const newPath = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
    setCurrentPath(newPath);
    setSelectedFiles(new Set());
  };

  const toggleFileSelection = (fsId: number) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fsId)) {
        newSet.delete(fsId);
      } else {
        newSet.add(fsId);
      }
      return newSet;
    });
  };

  const selectAll = () => {
    setSelectedFiles(new Set(files.map(f => f.fs_id)));
  };

  const clearSelection = () => {
    setSelectedFiles(new Set());
  };

  const downloadSelectedFolders = async () => {
    const selectedFolders = files.filter(f => f.is_dir && selectedFiles.has(f.fs_id));
    
    if (selectedFolders.length === 0) {
      addLog('请先选择要下载的文件夹');
      return;
    }

    addLog(`准备下载 ${selectedFolders.length} 个文件夹`);
    
    for (const folder of selectedFolders) {
      addLog(`开始处理文件夹: ${folder.server_filename}`);
      // 这里可以添加递归获取文件夹内容和下载的逻辑
    }
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '文件夹';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // 如果未登录，显示登录界面
  if (!isLoggedIn) {
    return <LoginScreen onLoginSuccess={handleLoginSuccess} />;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'system':
        return (
          <div className="p-6 space-y-6">
            <NetworkTest />
            <SystemInfo />
          </div>
        );
      case 'parse':
        return <ParsePage token={token} onLog={addLog} />;
      case 'logs':
        return (
          <div className="p-6">
            <LogPanel logs={logs} />
          </div>
        );
      case 'profile':
        return <ProfilePage token={token} tokenData={tokenData} />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <ParsePage token={token} onLog={addLog} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 侧边栏 */}
      <Sidebar
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onLogout={handleLogout}
      />

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 主内容 */}
        <div className="flex-1 overflow-hidden">
          {renderContent()}
        </div>

        {/* 状态栏 */}
        <StatusBar
          connectionStatus={connectionStatus}
          selectedCount={selectedFiles.size}
          totalFiles={files.length}
          currentOperation={currentOperation}
        />
      </div>



      {/* 对话框 */}
      <AboutDialog
        isOpen={showAbout}
        onClose={() => setShowAbout(false)}
      />
    </div>
  );
}

export default App;
