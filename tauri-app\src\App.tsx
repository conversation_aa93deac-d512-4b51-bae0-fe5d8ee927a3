import React, { useState, useCallback, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import {
  Download,
  Folder,
  File,
  ArrowLeft,
  Settings,
  Play,
  CheckSquare,
  Square,
  Copy,
  RefreshCw,
  Minimize2,
  Maximize2,
  X
} from 'lucide-react';
import { FileList } from './components/FileList';
import { SettingsPanel } from './components/SettingsPanel';
import { LogPanel } from './components/LogPanel';
import { MenuBar } from './components/MenuBar';
import { StatusBar } from './components/StatusBar';
import { SystemInfo } from './components/SystemInfo';
import { LicenseInfo } from './components/LicenseInfo';
import { AboutDialog } from './components/AboutDialog';
import { LoginScreen } from './components/LoginScreen';
import { NetworkCheck } from './components/NetworkCheck';

interface FileInfo {
  fs_id: number;
  is_dir: boolean;
  server_filename: string;
  size: number;
  path: string;
  server_mtime: number;
  selected?: boolean;
}

interface ApiResponse {
  code: number;
  message: string;
  data?: {
    uk?: number;
    shareid?: number;
    randsk?: string;
    list: FileInfo[];
  };
}

interface TokenData {
  count: number;
  expires_at?: string;
  ip: string[];
  remaining_count: number;
  remaining_size: number;
  size: number;
  token: string;
  used_at: string;
}

function App() {
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [tokenData, setTokenData] = useState<TokenData | null>(null);

  // 应用状态
  const [shareUrl, setShareUrl] = useState('https://pan.baidu.com/s/1mVe3LrL7US1MF5jICzmycg?');
  const [password, setPassword] = useState('1111');
  const [token, setToken] = useState('');
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Set<number>>(new Set());
  const [showAbout, setShowAbout] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting' | 'checking'>('disconnected');
  const [currentOperation, setCurrentOperation] = useState<string | undefined>();

  // 处理登录成功
  const handleLoginSuccess = (validatedToken: string, data: TokenData) => {
    setToken(validatedToken);
    setTokenData(data);
    setIsLoggedIn(true);
    addLog(`登录成功！欢迎使用，剩余次数: ${data.remaining_count.toLocaleString()}`);
  };

  // 处理登出
  const handleLogout = () => {
    setIsLoggedIn(false);
    setToken('');
    setTokenData(null);
    setFiles([]);
    setSelectedFiles(new Set());
    setCurrentPath('/');
    setConnectionStatus('disconnected');
    setLogs([]);
  };

  // 加载配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await invoke('load_config');
        console.log('加载的配置:', config);

        // 如果有保存的token，尝试自动验证
        if (config.token) {
          try {
            const response = await invoke('validate_token', { token: config.token });
            if (response.code === 200 && response.data) {
              handleLoginSuccess(config.token, response.data);
            }
          } catch (error) {
            console.log('自动登录失败:', error);
          }
        }
      } catch (error) {
        console.error('加载配置失败:', error);
      }
    };
    loadConfig();
  }, []);

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  }, []);

  const getFileList = async () => {
    if (!shareUrl || !password || !token) {
      addLog('请填写完整的分享链接、提取码和解析卡密');
      return;
    }

    setLoading(true);
    setConnectionStatus('connecting');
    setCurrentOperation('获取文件列表...');
    addLog(`开始获取文件列表... 路径: ${currentPath}`);

    try {
      const response: ApiResponse = await invoke('get_file_list', {
        token,
        url: shareUrl,
        pwd: password,
        dir: currentPath,
      });

      if (response.code === 200 && response.data) {
        setFiles(response.data.list);
        setConnectionStatus('connected');
        addLog(`成功获取 ${response.data.list.length} 个文件/文件夹`);
      } else {
        setConnectionStatus('disconnected');
        addLog(`获取文件列表失败: ${response.message}`);
      }
    } catch (error) {
      setConnectionStatus('disconnected');
      addLog(`获取文件列表出错: ${error}`);
    } finally {
      setLoading(false);
      setCurrentOperation(undefined);
    }
  };

  const enterFolder = (folderPath: string) => {
    setCurrentPath(folderPath);
    setSelectedFiles(new Set());
  };

  const goBack = () => {
    if (currentPath === '/') return;
    
    const pathParts = currentPath.split('/').filter(Boolean);
    pathParts.pop();
    const newPath = pathParts.length > 0 ? '/' + pathParts.join('/') : '/';
    setCurrentPath(newPath);
    setSelectedFiles(new Set());
  };

  const toggleFileSelection = (fsId: number) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fsId)) {
        newSet.delete(fsId);
      } else {
        newSet.add(fsId);
      }
      return newSet;
    });
  };

  const selectAll = () => {
    setSelectedFiles(new Set(files.map(f => f.fs_id)));
  };

  const clearSelection = () => {
    setSelectedFiles(new Set());
  };

  const downloadSelectedFolders = async () => {
    const selectedFolders = files.filter(f => f.is_dir && selectedFiles.has(f.fs_id));
    
    if (selectedFolders.length === 0) {
      addLog('请先选择要下载的文件夹');
      return;
    }

    addLog(`准备下载 ${selectedFolders.length} 个文件夹`);
    
    for (const folder of selectedFolders) {
      addLog(`开始处理文件夹: ${folder.server_filename}`);
      // 这里可以添加递归获取文件夹内容和下载的逻辑
    }
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '文件夹';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // 如果未登录，显示登录界面
  if (!isLoggedIn) {
    return <LoginScreen onLoginSuccess={handleLoginSuccess} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 菜单栏 */}
      <MenuBar
        onShowAbout={() => setShowAbout(true)}
        onShowSettings={() => setShowSettings(!showSettings)}
        onShowHelp={() => setShowHelp(true)}
      />

      {/* 标题栏 */}
      <div className="bg-white shadow-sm border-b">
        <div className="flex justify-between items-center px-4 py-3">
          <h1 className="text-xl font-bold text-gray-900">百度网盘解析工具</h1>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-outline text-sm"
            >
              <Settings className="w-4 h-4 mr-1" />
              设置
            </button>
            <button
              onClick={handleLogout}
              className="btn-outline text-sm text-red-600 hover:text-red-700"
            >
              <X className="w-4 h-4 mr-1" />
              退出登录
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 h-full">
            {/* Main Content */}
            <div className="lg:col-span-3 space-y-4 overflow-y-auto">
            {/* Input Section */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold">分享信息</h2>
              </div>
              <div className="card-content space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    分享链接
                  </label>
                  <input
                    type="text"
                    value={shareUrl}
                    onChange={(e) => setShareUrl(e.target.value)}
                    className="input"
                    placeholder="请输入百度网盘分享链接"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      提取码
                    </label>
                    <input
                      type="text"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="input"
                      placeholder="提取码"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      解析卡密
                    </label>
                    <input
                      type="text"
                      value={token}
                      onChange={(e) => setToken(e.target.value)}
                      className="input"
                      placeholder="解析卡密"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="card">
              <div className="card-content">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={goBack}
                      disabled={currentPath === '/'}
                      className="btn-outline disabled:opacity-50"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      返回上级
                    </button>
                    <span className="text-sm text-gray-600">
                      当前路径: {currentPath}
                    </span>
                  </div>
                  <button
                    onClick={getFileList}
                    disabled={loading}
                    className="btn-primary"
                  >
                    {loading ? (
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <RefreshCw className="w-4 h-4 mr-2" />
                    )}
                    获取文件列表
                  </button>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="card">
              <div className="card-content">
                <div className="flex flex-wrap gap-2">
                  <button onClick={selectAll} className="btn-outline">
                    <CheckSquare className="w-4 h-4 mr-2" />
                    全选
                  </button>
                  <button onClick={clearSelection} className="btn-outline">
                    <Square className="w-4 h-4 mr-2" />
                    清除选择
                  </button>
                  <button onClick={downloadSelectedFolders} className="btn-primary">
                    <Download className="w-4 h-4 mr-2" />
                    下载选中文件夹
                  </button>
                  <button className="btn-outline">
                    <Copy className="w-4 h-4 mr-2" />
                    复制所有链接
                  </button>
                </div>
                <div className="mt-2 text-sm text-blue-600">
                  💡 使用说明: 双击选中文件/文件夹 | 右键文件夹可进入 | 选中文件夹后点击'下载选中文件夹'自动创建同名目录下载
                </div>
              </div>
            </div>

            {/* File List */}
            <FileList
              files={files}
              selectedFiles={selectedFiles}
              onToggleSelection={toggleFileSelection}
              onEnterFolder={enterFolder}
              formatSize={formatSize}
              formatTime={formatTime}
            />
          </div>

            {/* Sidebar */}
            <div className="space-y-4 overflow-y-auto">
              <NetworkCheck onStatusChange={setConnectionStatus} />
              <SystemInfo />
              <LicenseInfo token={token} tokenData={tokenData || undefined} />
              {showSettings && <SettingsPanel />}
              <LogPanel logs={logs} />
            </div>
          </div>
        </div>
      </div>

      {/* 状态栏 */}
      <StatusBar
        connectionStatus={connectionStatus}
        selectedCount={selectedFiles.size}
        totalFiles={files.length}
        currentOperation={currentOperation}
      />

      {/* 对话框 */}
      <AboutDialog
        isOpen={showAbout}
        onClose={() => setShowAbout(false)}
      />
    </div>
  );
}

export default App;
