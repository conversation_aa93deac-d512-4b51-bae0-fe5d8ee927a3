[package]
name = "baidu-pan-parser"
version = "1.0.0"
description = "百度网盘解析工具"
authors = ["Your Name"]
license = "MIT"
repository = ""
default-run = "baidu-pan-parser"
edition = "2021"
rust-version = "1.60"

[build-dependencies]
tauri-build = { version = "1.5.0", features = [], default-features = false }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5.0", features = [ "fs-remove-file", "dialog-open", "fs-read-dir", "fs-create-dir", "shell-open", "clipboard-all", "fs-write-file", "fs-exists", "http-all", "fs-rename-file", "fs-read-file", "notification-all", "fs-remove-dir", "dialog-save", "system-tray", "os-all", "process-all"] }
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1.0", features = ["full"] }
uuid = { version = "1.0", features = ["v4"] }
sysinfo = "0.29"
chrono = { version = "0.4", features = ["serde"] }
md5 = "0.7"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
